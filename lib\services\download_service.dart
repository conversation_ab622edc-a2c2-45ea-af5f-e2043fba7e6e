import 'dart:io' if (dart.library.html) 'package:flutter_application_2/utils/web_file_stub.dart';
import 'package:flutter/foundation.dart';
import 'package:file_saver/file_saver.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;
import 'package:url_launcher/url_launcher.dart';
import 'package:open_file/open_file.dart';
// import 'package:background_downloader/background_downloader.dart'; // تم تعليقه مؤقتًا بسبب مشكلة التوافق


import '../utils/file_processor.dart';

/// خدمة تنزيل الملفات
/// توفر وظائف لتنزيل الملفات على مختلف المنصات (ويب، ويندوز، أندرويد، ماك، لينكس)
///
/// تم تحسين الخدمة لتوفير:
/// - دعم التنزيل في الخلفية باستخدام حزمة background_downloader
/// - شريط تقدم للتنزيل مع عرض نسبة التقدم والوقت المتبقي
/// - إشعارات لإعلام المستخدم بانتهاء عملية التنزيل
/// - دعم استئناف التنزيل في حالة انقطاع الاتصال
class DownloadService {
  // Singleton instance
  static final DownloadService _instance = DownloadService._internal();
  factory DownloadService() => _instance;

  DownloadService._internal() {
    // تهيئة خدمة التنزيل في الخلفية
    // لا نحتاج إلى تهيئة خاصة، سنستخدم الإعدادات الافتراضية
  }

  /// تنزيل ملف مرفق
  ///
  /// [attachment] المرفق المراد تنزيله
  /// [onProgress] دالة يتم استدعاؤها لتحديث نسبة التقدم
  /// [onComplete] دالة يتم استدعاؤها عند اكتمال التنزيل
  /// [openAfterDownload] فتح الملف بعد التنزيل
  ///
  /// يعيد قيمة منطقية تشير إلى نجاح أو فشل عملية التنزيل
  Future<bool> downloadAttachment(
    Attachment attachment, {
    Function(double)? onProgress,
    Function(bool)? onComplete,
    bool openAfterDownload = false,
  }) async {
    try {
      if (kIsWeb) {
        // في حالة الويب، نستخدم FileSaver
        final result = await _downloadAttachmentWeb(attachment);

        if (onComplete != null) {
          onComplete(result);
        }

        return result;
      } else {
        // في حالة التطبيقات المحلية، نستخدم طريقة بديلة مؤقتًا
        // تم تعليق استخدام background_downloader بسبب مشكلة التوافق

        // الحصول على مسار مجلد التنزيلات
        final downloadsPath = await _getDownloadsPath();
        final downloadFilePath = path.join(downloadsPath, attachment.fileName);

        // محاكاة التنزيل
        if (onProgress != null) {
          // محاكاة تقدم التنزيل
          for (int i = 0; i <= 10; i++) {
            await Future.delayed(const Duration(milliseconds: 100));
            onProgress(i / 10);
          }
        }

        // نسخ الملف إلى مجلد التنزيلات
        final sourceFile = File(attachment.filePath);
        if (await sourceFile.exists()) {
          await sourceFile.copy(downloadFilePath);

          // فتح الملف بعد التنزيل إذا كان مطلوبًا
          if (openAfterDownload) {
            await openFile(downloadFilePath);
          }

          if (onComplete != null) {
            onComplete(true);
          }

          return true;
        } else {
          debugPrint('الملف غير موجود: ${attachment.filePath}');
          if (onComplete != null) {
            onComplete(false);
          }
          return false;
        }
      }
    } catch (e) {
      debugPrint('خطأ في تنزيل الملف: $e');
      if (onComplete != null) {
        onComplete(false);
      }
      return false;
    }
  }

  /// تنزيل ملف مرفق في الويب
  Future<bool> _downloadAttachmentWeb(Attachment attachment) async {
    try {
      // التحقق من وجود الملف
      final file = File(attachment.filePath);
      if (!await file.exists()) {
        debugPrint('الملف غير موجود: ${attachment.filePath}');
        return false;
      }

      // قراءة محتوى الملف
      final bytes = await file.readAsBytes();

      // تحديد نوع الملف
      final fileType = FileProcessor.getFileType(attachment.filePath);
      final mimeType = _getMimeType(fileType, attachment.filePath);

      // تنزيل الملف باستخدام file_saver
      await FileSaver.instance.saveFile(
        name: attachment.fileName,
        bytes: bytes,
        ext: path.extension(attachment.filePath).replaceAll('.', ''),
        mimeType: mimeType,
      );

      return true;
    } catch (e) {
      debugPrint('خطأ في تنزيل الملف على الويب: $e');
      return false;
    }
  }

  /// تنزيل ملف مرفق في التطبيقات المحلية - تم تعليقه مؤقتًا
  // Future<bool> _downloadAttachmentNative(
  //   Attachment attachment, {
  //   Function(double)? onProgress,
  //   Function(bool)? onComplete,
  // }) async {
  //   // تم تعليق هذه الوظيفة مؤقتًا بسبب مشكلة التوافق مع background_downloader
  //   return true;
  // }

  /// تنزيل ملف من مسار محدد
  ///
  /// [filePath] مسار الملف المراد تنزيله
  /// [fileName] اسم الملف المراد تنزيله
  /// [onProgress] دالة يتم استدعاؤها لتحديث نسبة التقدم
  /// [onComplete] دالة يتم استدعاؤها عند اكتمال التنزيل مع مسار الملف المنزل
  /// [openAfterDownload] فتح الملف بعد التنزيل
  ///
  /// يعيد قيمة منطقية تشير إلى نجاح أو فشل عملية التنزيل
  Future<Map<String, dynamic>> downloadFile(
    String filePath,
    String fileName, {
    Function(double)? onProgress,
    Function(Map<String, dynamic>)? onComplete,
    bool openAfterDownload = false,
  }) async {
    try {
      if (kIsWeb) {
        // في حالة الويب، نستخدم FileSaver
        final result = await _downloadFileWeb(filePath, fileName);
        final resultMap = {
          'success': result,
          'downloadPath': 'مجلد التنزيلات في المتصفح',
          'fileName': fileName,
        };

        if (onComplete != null) {
          onComplete(resultMap);
        }

        return resultMap;
      } else {
        // في حالة التطبيقات المحلية، نستخدم طريقة بديلة مؤقتًا
        // تم تعليق استخدام background_downloader بسبب مشكلة التوافق

        // الحصول على مسار مجلد التنزيلات
        final downloadsPath = await _getDownloadsPath();
        final downloadFilePath = path.join(downloadsPath, fileName);

        // محاكاة التنزيل
        if (onProgress != null) {
          // محاكاة تقدم التنزيل
          for (int i = 0; i <= 10; i++) {
            await Future.delayed(const Duration(milliseconds: 100));
            onProgress(i / 10);
          }
        }

        // نسخ الملف إلى مجلد التنزيلات
        final sourceFile = File(filePath);
        if (await sourceFile.exists()) {
          await sourceFile.copy(downloadFilePath);

          // فتح الملف بعد التنزيل إذا كان مطلوبًا
          if (openAfterDownload) {
            await openFile(downloadFilePath);
          }

          final resultMap = {
            'success': true,
            'downloadPath': downloadsPath,
            'fileName': fileName,
            'fullPath': downloadFilePath,
          };

          if (onComplete != null) {
            onComplete(resultMap);
          }

          return resultMap;
        } else {
          final resultMap = {
            'success': false,
            'error': 'الملف غير موجود: $filePath',
          };

          if (onComplete != null) {
            onComplete(resultMap);
          }

          return resultMap;
        }
      }
    } catch (e) {
      debugPrint('خطأ في تنزيل الملف: $e');
      final resultMap = {
        'success': false,
        'error': e.toString(),
      };

      if (onComplete != null) {
        onComplete(resultMap);
      }

      return resultMap;
    }
  }

  /// الحصول على مسار مجلد التنزيلات
  Future<String> _getDownloadsPath() async {
    try {
      if (kIsWeb) {
        return 'مجلد التنزيلات في المتصفح';
      }

      if (Platform.isWindows) {
        // في ويندوز، نستخدم مجلد التنزيلات الافتراضي
        final home = Platform.environment['USERPROFILE'] ?? r'C:\Users\<USER>\Users\Downloads';
    }
  }

  /// فتح الملف باستخدام التطبيق الافتراضي في نظام التشغيل
  ///
  /// [filePath] مسار الملف المراد فتحه
  ///
  /// يعيد نتيجة عملية الفتح
  Future<Map<String, dynamic>> openFile(String filePath) async {
    try {
      if (kIsWeb) {
        // في حالة الويب، نستخدم url_launcher
        final uri = Uri.file(filePath);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
          return {
            'success': true,
            'message': 'تم فتح الملف بنجاح',
            'filePath': filePath,
          };
        } else {
          return {
            'success': false,
            'message': 'لا يمكن فتح الملف في المتصفح',
            'filePath': filePath,
          };
        }
      } else {
        // في حالة التطبيقات المحلية، نستخدم open_file
        debugPrint('فتح الملف: $filePath');
        final result = await OpenFile.open(filePath);
        return {
          'success': result.type == ResultType.done,
          'message': result.message,
          'filePath': filePath,
        };
      }
    } catch (e) {
      debugPrint('خطأ في فتح الملف: $e');
      return {
        'success': false,
        'message': 'حدث خطأ أثناء فتح الملف: $e',
        'filePath': filePath,
        'error': e.toString(),
      };
    }
  }

  /// فتح الملف بعد تنزيله
  ///
  /// [downloadPath] مسار مجلد التنزيل
  /// [fileName] اسم الملف
  ///
  /// يعيد نتيجة عملية الفتح
  Future<Map<String, dynamic>> openDownloadedFile(String downloadPath, String fileName) async {
    try {
      final filePath = path.join(downloadPath, fileName);
      debugPrint('فتح الملف المنزل: $filePath');

      // التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        return {
          'success': false,
          'message': 'الملف غير موجود: $filePath',
          'filePath': filePath,
        };
      }

      return await openFile(filePath);
    } catch (e) {
      debugPrint('خطأ في فتح الملف المنزل: $e');
      return {
        'success': false,
        'message': 'حدث خطأ أثناء فتح الملف المنزل: $e',
        'error': e.toString(),
      };
    }
  }

  /// تحديد ما إذا كان الملف يمكن عرضه داخل التطبيق
  ///
  /// [filePath] مسار الملف
  ///
  /// يعيد قيمة منطقية تشير إلى إمكانية عرض الملف داخل التطبيق
  bool canViewInApp(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    final fileType = FileProcessor.getFileType(filePath);

    // يمكن عرض ملفات PDF والصور داخل التطبيق
    return fileType == 'pdf' ||
           fileType == 'image' ||
           ['.pdf', '.jpg', '.jpeg', '.png', '.gif', '.bmp'].contains(extension);
  }

  /// الحصول على نوع الملف المناسب للعرض
  ///
  /// [filePath] مسار الملف
  ///
  /// يعيد نوع الملف للعرض (pdf, image, other)
  String getViewerType(String filePath) {
    final extension = path.extension(filePath).toLowerCase();

    if (extension == '.pdf') {
      return 'pdf';
    } else if (['.jpg', '.jpeg', '.png', '.gif', '.bmp'].contains(extension)) {
      return 'image';
    } else {
      return 'other';
    }
  }

  /// تنزيل ملف في الويب
  Future<bool> _downloadFileWeb(String filePath, String fileName) async {
    try {
      // التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('الملف غير موجود: $filePath');
        return false;
      }

      // قراءة محتوى الملف
      final bytes = await file.readAsBytes();

      // تحديد نوع الملف
      final fileType = FileProcessor.getFileType(filePath);
      final mimeType = _getMimeType(fileType, filePath);

      // تنزيل الملف باستخدام file_saver
      await FileSaver.instance.saveFile(
        name: fileName,
        bytes: bytes,
        ext: path.extension(filePath).replaceAll('.', ''),
        mimeType: mimeType,
      );

      return true;
    } catch (e) {
      debugPrint('خطأ في تنزيل الملف على الويب: $e');
      return false;
    }
  }

  /// تنزيل ملف في التطبيقات المحلية - تم تعليقه مؤقتًا
  // Future<bool> _downloadFileNative(
  //   String filePath,
  //   String fileName, {
  //   Function(double)? onProgress,
  //   Function(bool)? onComplete,
  // }) async {
  //   // تم تعليق هذه الوظيفة مؤقتًا بسبب مشكلة التوافق مع background_downloader
  //   return true;
  // }

  /// تنزيل بيانات ثنائية كملف
  ///
  /// [bytes] البيانات الثنائية المراد تنزيلها
  /// [fileName] اسم الملف المراد تنزيله
  /// [extension] امتداد الملف
  ///
  /// يعيد قيمة منطقية تشير إلى نجاح أو فشل عملية التنزيل
  Future<bool> downloadBytes(Uint8List bytes, String fileName, String extension) async {
    try {
      // تحديد نوع الملف
      final mimeType = _getMimeTypeFromExtension(extension);

      // تنزيل الملف باستخدام file_saver
      await FileSaver.instance.saveFile(
        name: fileName,
        bytes: bytes,
        ext: extension,
        mimeType: mimeType,
      );

      return true;
    } catch (e) {
      debugPrint('خطأ في تنزيل البيانات: $e');
      return false;
    }
  }

  /// الحصول على نوع MIME للملف
  ///
  /// [fileType] نوع الملف
  /// [filePath] مسار الملف
  ///
  /// يعيد نوع MIME المناسب للملف
  MimeType _getMimeType(String fileType, String filePath) {
    final extension = path.extension(filePath).toLowerCase();

    switch (fileType) {
      case 'image':
        if (extension == '.jpg' || extension == '.jpeg') {
          return MimeType.jpeg;
        } else if (extension == '.png') {
          return MimeType.png;
        } else if (extension == '.gif') {
          return MimeType.gif;
        } else if (extension == '.bmp') {
          return MimeType.bmp;
        } else {
          return MimeType.png; // Default to PNG for other image types
        }
      case 'document':
        if (extension == '.pdf') {
          return MimeType.pdf;
        } else if (['.doc', '.docx'].contains(extension)) {
          return MimeType.microsoftWord;
        } else if (['.xls', '.xlsx'].contains(extension)) {
          return MimeType.microsoftExcel;
        } else if (['.ppt', '.pptx'].contains(extension)) {
          return MimeType.microsoftPresentation;
        } else {
          return MimeType.text;
        }
      case 'video':
        if (extension == '.mp4') {
          return MimeType.mp4Video;
        } else if (extension == '.avi') {
          return MimeType.avi;
        } else if (extension == '.mpeg') {
          return MimeType.mpeg;
        } else {
          return MimeType.mp4Video; // Default to MP4 for other video types
        }
      case 'audio':
        if (extension == '.mp3') {
          return MimeType.mp3;
        } else if (extension == '.aac') {
          return MimeType.aac;
        } else if (extension == '.mp4') {
          return MimeType.mp4Audio;
        } else {
          return MimeType.mp3; // Default to MP3 for other audio types
        }
      case 'archive':
        if (extension == '.zip') {
          return MimeType.zip;
        } else if (extension == '.rar') {
          return MimeType.rar;
        } else {
          return MimeType.other; // Default to other for archives
        }
      default:
        // استخدام مكتبة mime للكشف عن نوع الملف
        final mimeTypeStr = lookupMimeType(filePath);
        if (mimeTypeStr != null) {
          if (mimeTypeStr.startsWith('image/')) {
            if (mimeTypeStr.contains('jpeg') || mimeTypeStr.contains('jpg')) {
              return MimeType.jpeg;
            } else if (mimeTypeStr.contains('png')) {
              return MimeType.png;
            } else if (mimeTypeStr.contains('gif')) {
              return MimeType.gif;
            } else if (mimeTypeStr.contains('bmp')) {
              return MimeType.bmp;
            } else {
              return MimeType.png; // Default to PNG for other image types
            }
          } else if (mimeTypeStr.startsWith('video/')) {
            if (mimeTypeStr.contains('mp4')) {
              return MimeType.mp4Video;
            } else if (mimeTypeStr.contains('avi')) {
              return MimeType.avi;
            } else if (mimeTypeStr.contains('mpeg')) {
              return MimeType.mpeg;
            } else {
              return MimeType.mp4Video; // Default to MP4 for other video types
            }
          } else if (mimeTypeStr.startsWith('audio/')) {
            if (mimeTypeStr.contains('mp3') || mimeTypeStr.contains('mpeg')) {
              return MimeType.mp3;
            } else if (mimeTypeStr.contains('aac')) {
              return MimeType.aac;
            } else if (mimeTypeStr.contains('mp4')) {
              return MimeType.mp4Audio;
            } else {
              return MimeType.mp3; // Default to MP3 for other audio types
            }
          } else if (mimeTypeStr.startsWith('text/')) {
            return MimeType.text;
          } else if (mimeTypeStr == 'application/pdf') {
            return MimeType.pdf;
          } else if (mimeTypeStr.contains('word')) {
            return MimeType.microsoftWord;
          } else if (mimeTypeStr.contains('excel') || mimeTypeStr.contains('spreadsheet')) {
            return MimeType.microsoftExcel;
          } else if (mimeTypeStr.contains('powerpoint') || mimeTypeStr.contains('presentation')) {
            return MimeType.microsoftPresentation;
          }
        }
        return MimeType.other; // Changed from binary to other
    }
  }

  /// الحصول على نوع MIME من امتداد الملف
  ///
  /// [extension] امتداد الملف
  ///
  /// يعيد نوع MIME المناسب للامتداد
  MimeType _getMimeTypeFromExtension(String extension) {
    final ext = extension.toLowerCase().replaceAll('.', '');

    // صور
    if (['jpg', 'jpeg'].contains(ext)) {
      return MimeType.jpeg;
    } else if (ext == 'png') {
      return MimeType.png;
    } else if (ext == 'gif') {
      return MimeType.gif;
    } else if (ext == 'bmp') {
      return MimeType.bmp;
    } else if (['webp', 'svg'].contains(ext)) {
      return MimeType.png; // Default to PNG for other image types
    }

    // مستندات
    if (ext == 'pdf') {
      return MimeType.pdf;
    } else if (['doc', 'docx'].contains(ext)) {
      return MimeType.microsoftWord;
    } else if (['xls', 'xlsx', 'csv'].contains(ext)) {
      return MimeType.microsoftExcel;
    } else if (['ppt', 'pptx'].contains(ext)) {
      return MimeType.microsoftPresentation;
    } else if (['txt', 'rtf', 'md'].contains(ext)) {
      return MimeType.text;
    }

    // وسائط
    if (ext == 'mp4') {
      return MimeType.mp4Video;
    } else if (ext == 'avi') {
      return MimeType.avi;
    } else if (ext == 'mpeg') {
      return MimeType.mpeg;
    } else if (['mov', 'wmv', 'flv', 'mkv', 'webm'].contains(ext)) {
      return MimeType.mp4Video; // Default to MP4 for other video types
    } else if (ext == 'mp3') {
      return MimeType.mp3;
    } else if (ext == 'aac') {
      return MimeType.aac;
    } else if (['wav', 'ogg', 'wma'].contains(ext)) {
      return MimeType.mp3; // Default to MP3 for other audio types
    }

    // أرشيف
    if (ext == 'zip') {
      return MimeType.zip;
    } else if (ext == 'rar') {
      return MimeType.rar;
    } else if (['7z', 'tar', 'gz'].contains(ext)) {
      return MimeType.other; // Default to other for archives
    }

    // افتراضي
    return MimeType.other;
  }
}
