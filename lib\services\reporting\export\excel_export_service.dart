import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:excel/excel.dart';
import 'dart:typed_data';


import '../../../controllers/auth_controller.dart';
import 'file_handler_service.dart';

/// خدمة تصدير التقارير بتنسيق Excel
///
/// توفر وظائف لتصدير نتائج التقارير إلى ملفات Excel
class ExcelExportService extends GetxService {
  final FileHandlerService _fileHandler = FileHandlerService();

  /// تصدير تقرير إلى ملف Excel
  Future<String?> exportToExcel({
    required String reportId,
    required String title,
    required List<Map<String, dynamic>> data,
    required Map<String, dynamic> summary,
    Map<String, List<Map<String, dynamic>>>? visualizationData,
    String? description,
    ReportType? reportType,
  }) async {
    try {
      // إنشاء ملف Excel
      final excel = Excel.createExcel();

      // إنشاء ورقة الملخص
      final summarySheet = excel['الملخص'];
      _addSummarySheet(
        sheet: summarySheet,
        title: title,
        description: description,
        reportType: reportType,
        summary: summary,
      );

      // إنشاء ورقة البيانات
      final dataSheet = excel['البيانات'];
      _addDataSheet(
        sheet: dataSheet,
        data: data,
      );

      // إنشاء أوراق إضافية للتصورات المرئية
      if (visualizationData != null) {
        visualizationData.forEach((key, value) {
          final sheet = excel[key];
          _addVisualizationDataSheet(
            sheet: sheet,
            data: value,
            title: key,
          );
        });
      }

      // حفظ الملف
      final fileName = 'report_${reportId}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.xlsx';
      final bytes = excel.encode();

      if (bytes == null) {
        throw Exception('فشل في إنشاء ملف Excel');
      }

      final filePath = await _fileHandler.saveFile(
        fileName: fileName,
        bytes: Uint8List.fromList(bytes),
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );

      return filePath;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير إلى Excel: $e');
      return null;
    }
  }

  /// إضافة ورقة الملخص
  void _addSummarySheet({
    required Sheet sheet,
    required String title,
    String? description,
    ReportType? reportType,
    required Map<String, dynamic> summary,
  }) {
    // الحصول على معلومات المستخدم الحالي
    final authController = Get.find<AuthController>();
    final currentUser = authController.currentUser.value;

    // إضافة عنوان التقرير
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('تقرير: $title');
    sheet.cell(CellIndex.indexByString('A1')).cellStyle = CellStyle(
      bold: true,
      fontSize: 16,
    );

    // إضافة وصف التقرير
    if (description != null) {
      sheet.cell(CellIndex.indexByString('A2')).value = TextCellValue(description);
    }

    // إضافة معلومات التقرير
    int row = description != null ? 4 : 3;

    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('تاريخ التقرير:');
    sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue(DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now()));
    row++;

    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('تم إنشاؤه بواسطة:');
    sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue(currentUser?.name ?? 'مستخدم غير معروف');
    row++;

    if (reportType != null) {
      sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('نوع التقرير:');
      sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue(_getReportTypeName(reportType));
      row++;
    }

    // إضافة فاصل
    row += 2;
    sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue('ملخص التقرير');
    sheet.cell(CellIndex.indexByString('A$row')).cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
    );
    row++;

    // إضافة بيانات الملخص
    summary.forEach((key, value) {
      if (value is Map) {
        // إذا كانت القيمة خريطة، أضف عنوانًا فرعيًا وعناصر الخريطة
        sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(_formatSummaryKey(key));
        sheet.cell(CellIndex.indexByString('A$row')).cellStyle = CellStyle(
          bold: true,
        );
        row++;

        (value).forEach((subKey, subValue) {
          sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(_formatSummaryKey(subKey.toString()));
          sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue(subValue.toString());
          row++;
        });
      } else {
        // إذا كانت القيمة بسيطة، أضف صفًا واحدًا
        sheet.cell(CellIndex.indexByString('A$row')).value = TextCellValue(_formatSummaryKey(key));
        sheet.cell(CellIndex.indexByString('B$row')).value = TextCellValue(value.toString());
        row++;
      }
    });
  }

  /// إضافة ورقة البيانات
  void _addDataSheet({
    required Sheet sheet,
    required List<Map<String, dynamic>> data,
  }) {
    if (data.isEmpty) {
      sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('لا توجد بيانات');
      return;
    }

    // الحصول على أسماء الأعمدة
    final columns = data.first.keys.toList();

    // إضافة رأس الجدول
    for (int i = 0; i < columns.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(_formatSummaryKey(columns[i]));
      cell.cellStyle = CellStyle(
        bold: true,
      );
    }

    // إضافة صفوف البيانات
    for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
      final row = data[rowIndex];

      for (int colIndex = 0; colIndex < columns.length; colIndex++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex, rowIndex: rowIndex + 1));
        final value = row[columns[colIndex]];

        if (value is num) {
          cell.value = TextCellValue(value.toString());
        } else if (value is DateTime) {
          cell.value = TextCellValue(DateFormat('yyyy-MM-dd HH:mm').format(value));
        } else if (value is bool) {
          cell.value = TextCellValue(value ? 'نعم' : 'لا');
        } else {
          cell.value = TextCellValue(value?.toString() ?? '');
        }
      }
    }

    // تعيين عرض الأعمدة
    for (int i = 0; i < columns.length; i++) {
      sheet.setColumnWidth(i, 20);
    }
  }

  /// إضافة ورقة بيانات التصور المرئي
  void _addVisualizationDataSheet({
    required Sheet sheet,
    required List<Map<String, dynamic>> data,
    required String title,
  }) {
    if (data.isEmpty) {
      sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('لا توجد بيانات');
      return;
    }

    // إضافة عنوان
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue(title);
    sheet.cell(CellIndex.indexByString('A1')).cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
    );

    // الحصول على أسماء الأعمدة
    final columns = data.first.keys.toList();

    // إضافة رأس الجدول
    for (int i = 0; i < columns.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 2));
      cell.value = TextCellValue(_formatSummaryKey(columns[i]));
      cell.cellStyle = CellStyle(
        bold: true,
      );
    }

    // إضافة صفوف البيانات
    for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
      final row = data[rowIndex];

      for (int colIndex = 0; colIndex < columns.length; colIndex++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex, rowIndex: rowIndex + 3));
        final value = row[columns[colIndex]];

        if (value is num) {
          cell.value = TextCellValue(value.toString());
        } else if (value is DateTime) {
          cell.value = TextCellValue(DateFormat('yyyy-MM-dd HH:mm').format(value));
        } else if (value is bool) {
          cell.value = TextCellValue(value ? 'نعم' : 'لا');
        } else {
          cell.value = TextCellValue(value?.toString() ?? '');
        }
      }
    }

    // تعيين عرض الأعمدة
    for (int i = 0; i < columns.length; i++) {
      sheet.setColumnWidth(i, 20);
    }
  }

  /// تحويل لون من سلسلة Hex إلى كائن ExcelColor
  String getColorFromHex(String hexColor) {
    // إزالة علامة # إذا كانت موجودة
    if (hexColor.startsWith('#')) {
      hexColor = hexColor.substring(1);
    }

    return hexColor;
  }

  /// تنسيق مفتاح الملخص
  String _formatSummaryKey(String key) {
    // تحويل camelCase إلى كلمات منفصلة
    final formattedKey = key.replaceAllMapped(
      RegExp(r'([A-Z])'),
      (match) => ' ${match.group(0)}',
    );

    // تحويل الحرف الأول إلى حرف كبير
    return formattedKey.substring(0, 1).toUpperCase() + formattedKey.substring(1);
  }

  /// الحصول على اسم نوع التقرير
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.taskStatus:
        return 'حالة المهام';
      case ReportType.userPerformance:
        return 'أداء المستخدم';
      case ReportType.departmentPerformance:
        return 'أداء القسم';
      case ReportType.timeTracking:
        return 'تتبع الوقت';
      case ReportType.taskProgress:
        return 'تقدم المهام';
      case ReportType.taskDetails:
        return 'تفاصيل المهمة';
      case ReportType.taskCompletion:
        return 'إكمال المهام';
      case ReportType.userActivity:
        return 'نشاط المستخدم';
      case ReportType.departmentWorkload:
        return 'عبء العمل للقسم';
      case ReportType.projectStatus:
        return 'حالة المشروع';
      case ReportType.custom:
        return 'تقرير مخصص';
      default:
        return 'غير معروف';
    }
  }
}
