import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';


import '../controllers/auth_controller.dart';


/// خدمة إدارة لوحة المعلومات
///
/// توفر وظائف لإدارة لوحات المعلومات المخصصة
class DashboardService extends GetxService {
  final DashboardRepository _dashboardRepository = Get.find<DashboardRepository>();
  final AuthController _authController = Get.find<AuthController>();

  // لوحة المعلومات الحالية
  final Rx<Dashboard?> currentDashboard = Rx<Dashboard?>(null);

  // قائمة لوحات المعلومات
  final RxList<Dashboard> dashboards = <Dashboard>[].obs;

  // حالة التحميل
  final RxBool isLoading = false.obs;

  // حالة الحفظ
  final RxBool isSaving = false.obs;

  // اللوحات المرتبطة
  final RxList<int> connectedBoardIds = <int>[].obs;

  // حالة تحديث البيانات
  final RxBool isRefreshing = false.obs;

  /// تهيئة الخدمة
  Future<DashboardService> init() async {
    await loadDashboards();
    return this;
  }

  /// تحديث بيانات لوحة المعلومات
  Future<void> refreshDashboardData() async {
    try {
      isRefreshing.value = true;

      // إعادة تحميل البيانات من قاعدة البيانات
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        throw Exception('لم يتم تسجيل الدخول');
      }

      // تحديث البيانات الحالية
      if (currentDashboard.value != null) {
        final updatedDashboard = await _dashboardRepository.getDashboardById(currentDashboard.value!.id);
        if (updatedDashboard != null) {
          currentDashboard.value = updatedDashboard;
        }
      }

      // تحديث قائمة لوحات المعلومات
      final loadedDashboards = await _dashboardRepository.getDashboardsByUserId(userId);
      dashboards.assignAll(loadedDashboards);
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات لوحة المعلومات: $e');
      rethrow;
    } finally {
      isRefreshing.value = false;
    }
  }

  /// الحصول على لوحات المعلومات للمستخدم
  Future<List<Dashboard>> getDashboardsByUserId(String userId) async {
    try {
      return await _dashboardRepository.getDashboardsByUserId(userId);
    } catch (e) {
      debugPrint('خطأ في الحصول على لوحات المعلومات للمستخدم: $e');
      return [];
    }
  }

  /// تحميل لوحات المعلومات للمستخدم
  Future<List<Dashboard>> loadDashboardsByUserId(String userId) async {
    try {
      final loadedDashboards = await _dashboardRepository.getDashboardsByUserId(userId);
      dashboards.assignAll(loadedDashboards);
      return loadedDashboards;
    } catch (e) {
      debugPrint('خطأ في تحميل لوحات المعلومات للمستخدم: $e');
      return [];
    }
  }

  /// الحصول على لوحة المعلومات بواسطة المعرف
  Future<Dashboard?> getDashboardById(String dashboardId) async {
    try {
      return await _dashboardRepository.getDashboardById(dashboardId);
    } catch (e) {
      debugPrint('خطأ في الحصول على لوحة المعلومات: $e');
      return null;
    }
  }

  /// تحميل لوحات المعلومات
  Future<void> loadDashboards() async {
    try {
      isLoading.value = true;

      // الحصول على معرف المستخدم الحالي
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        throw Exception('لم يتم تسجيل الدخول');
      }

      // تحميل لوحات المعلومات من قاعدة البيانات
      final loadedDashboards = await _dashboardRepository.getDashboardsByUserId(userId);
      dashboards.assignAll(loadedDashboards);

      // تحميل لوحة المعلومات الافتراضية إذا كانت موجودة
      final defaultDashboard = loadedDashboards.firstWhereOrNull((d) => d.isDefault);
      if (defaultDashboard != null) {
        currentDashboard.value = defaultDashboard;
      } else if (loadedDashboards.isNotEmpty) {
        currentDashboard.value = loadedDashboards.first;
      } else {
        // إنشاء لوحة معلومات افتراضية إذا لم تكن موجودة
        await createDefaultDashboard();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل لوحات المعلومات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// إنشاء لوحة معلومات افتراضية
  Future<void> createDefaultDashboard() async {
    try {
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        throw Exception('لم يتم تسجيل الدخول');
      }

      // إنشاء لوحة معلومات جديدة
      final dashboard = Dashboard(
        id: const Uuid().v4(),
        title: 'لوحة المعلومات الرئيسية',
        description: 'لوحة المعلومات الافتراضية',
        ownerId: userId,
        createdAt: DateTime.now(),
        widgets: _getDefaultWidgets(),
        isDefault: true,
        gridRows: 12,
        gridColumns: 12,
      );

      // حفظ لوحة المعلومات في قاعدة البيانات
      await _dashboardRepository.saveDashboard(dashboard);

      // إضافة لوحة المعلومات إلى القائمة
      dashboards.add(dashboard);
      currentDashboard.value = dashboard;
    } catch (e) {
      debugPrint('خطأ في إنشاء لوحة المعلومات الافتراضية: $e');
    }
  }

  /// إنشاء لوحة معلومات جديدة
  Future<Dashboard?> createNewDashboard({
    required String title,
    String? description,
    bool isDefault = false,
    int gridRows = 12,
    int gridColumns = 12,
  }) async {
    try {
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        throw Exception('لم يتم تسجيل الدخول');
      }

      // إنشاء لوحة معلومات جديدة
      final dashboard = Dashboard(
        id: const Uuid().v4(),
        title: title,
        description: description,
        ownerId: userId,
        createdAt: DateTime.now(),
        widgets: [], // بدون عناصر في البداية
        isDefault: isDefault,
        gridRows: gridRows,
        gridColumns: gridColumns,
      );

      // حفظ لوحة المعلومات في قاعدة البيانات
      await _dashboardRepository.saveDashboard(dashboard);

      // إضافة لوحة المعلومات إلى القائمة
      dashboards.add(dashboard);

      // تعيين لوحة المعلومات الحالية إذا كانت افتراضية أو إذا كانت هذه أول لوحة معلومات
      if (isDefault || dashboards.length == 1) {
        currentDashboard.value = dashboard;
      }

      return dashboard;
    } catch (e) {
      debugPrint('خطأ في إنشاء لوحة معلومات جديدة: $e');
      return null;
    }
  }

  /// الحصول على العناصر الافتراضية للوحة المعلومات
  List<DashboardWidget> _getDefaultWidgets() {
    return [
      // مخطط حالة المهام
      DashboardWidget(
        id: const Uuid().v4(),
        title: 'توزيع المهام حسب الحالة',
        type: DashboardWidgetType.taskStatusChart,
        settings: jsonEncode({
          'chartType': 'pie',
          'showLegend': true,
          'showValues': true,
          'showPercentages': true,
        }),
        rowIndex: 0,
        columnIndex: 0,
        width: 6,
        height: 4,
        isExpandable: true,
      ),

      // مخطط تقدم المهام
      DashboardWidget(
        id: const Uuid().v4(),
        title: 'تقدم المهام على مدار الوقت',
        type: DashboardWidgetType.taskProgressChart,
        settings: jsonEncode({
          'chartType': 'line',
          'showGrid': true,
          'showDots': true,
          'showBelowArea': true,
          'timeRange': 'month',
        }),
        rowIndex: 0,
        columnIndex: 6,
        width: 6,
        height: 4,
        isExpandable: true,
      ),

      // عدادات المهام
      DashboardWidget(
        id: const Uuid().v4(),
        title: 'ملخص المهام',
        type: DashboardWidgetType.taskCounters,
        settings: jsonEncode({
          'showTotal': true,
          'showCompleted': true,
          'showOverdue': true,
          'showInProgress': true,
        }),
        rowIndex: 4,
        columnIndex: 0,
        width: 12,
        height: 2,
        isExpandable: false,
      ),

      // مخطط أداء المستخدم
      DashboardWidget(
        id: const Uuid().v4(),
        title: 'أداء المستخدمين',
        type: DashboardWidgetType.userPerformanceChart,
        settings: jsonEncode({
          'chartType': 'bar',
          'showGrid': true,
          'showValues': true,
          'maxUsers': 5,
          'sortBy': 'completed',
        }),
        rowIndex: 6,
        columnIndex: 0,
        width: 6,
        height: 4,
        isExpandable: true,
      ),

      // مخطط أداء الأقسام
      DashboardWidget(
        id: const Uuid().v4(),
        title: 'أداء الأقسام',
        type: DashboardWidgetType.departmentPerformanceChart,
        settings: jsonEncode({
          'chartType': 'bar',
          'showGrid': true,
          'showValues': true,
          'sortBy': 'completed',
        }),
        rowIndex: 6,
        columnIndex: 6,
        width: 6,
        height: 4,
        isExpandable: true,
      ),
    ];
  }

  /// حفظ لوحة المعلومات
  Future<bool> saveDashboard(Dashboard dashboard) async {
    try {
      isSaving.value = true;

      // تحديث تاريخ التعديل
      final updatedDashboard = dashboard.copyWith(
        updatedAt: DateTime.now(),
      );

      // حفظ لوحة المعلومات في قاعدة البيانات
      await _dashboardRepository.saveDashboard(updatedDashboard);

      // تحديث القائمة
      final index = dashboards.indexWhere((d) => d.id == updatedDashboard.id);
      if (index >= 0) {
        dashboards[index] = updatedDashboard;
      } else {
        dashboards.add(updatedDashboard);
      }

      // تحديث لوحة المعلومات الحالية إذا كانت هي نفسها
      if (currentDashboard.value?.id == updatedDashboard.id) {
        currentDashboard.value = updatedDashboard;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في حفظ لوحة المعلومات: $e');
      return false;
    } finally {
      isSaving.value = false;
    }
  }

  /// تحديث موقع العنصر
  Future<bool> updateWidgetPosition(String widgetId, int rowIndex, int columnIndex) async {
    try {
      final dashboard = currentDashboard.value;
      if (dashboard == null) return false;

      // البحث عن العنصر
      final widgetIndex = dashboard.widgets.indexWhere((w) => w.id == widgetId);
      if (widgetIndex < 0) return false;

      // تحديث موقع العنصر
      final widget = dashboard.widgets[widgetIndex];
      final updatedWidget = widget.copyWith(
        rowIndex: rowIndex,
        columnIndex: columnIndex,
      );

      // إنشاء قائمة جديدة من العناصر
      final updatedWidgets = List<DashboardWidget>.from(dashboard.widgets);
      updatedWidgets[widgetIndex] = updatedWidget;

      // تحديث لوحة المعلومات
      final updatedDashboard = dashboard.copyWith(
        widgets: updatedWidgets,
        updatedAt: DateTime.now(),
      );

      // حفظ التغييرات
      return await saveDashboard(updatedDashboard);
    } catch (e) {
      debugPrint('خطأ في تحديث موقع العنصر: $e');
      return false;
    }
  }

  /// تحديث إعدادات العنصر
  Future<bool> updateWidgetSettings(String widgetId, Map<String, dynamic> settings) async {
    try {
      final dashboard = currentDashboard.value;
      if (dashboard == null) return false;

      // البحث عن العنصر
      final widgetIndex = dashboard.widgets.indexWhere((w) => w.id == widgetId);
      if (widgetIndex < 0) return false;

      // تحديث إعدادات العنصر
      final widget = dashboard.widgets[widgetIndex];
      final updatedWidget = widget.copyWith(
        settings: jsonEncode(settings),
      );

      // إنشاء قائمة جديدة من العناصر
      final updatedWidgets = List<DashboardWidget>.from(dashboard.widgets);
      updatedWidgets[widgetIndex] = updatedWidget;

      // تحديث لوحة المعلومات
      final updatedDashboard = dashboard.copyWith(
        widgets: updatedWidgets,
        updatedAt: DateTime.now(),
      );

      // حفظ التغييرات
      return await saveDashboard(updatedDashboard);
    } catch (e) {
      debugPrint('خطأ في تحديث إعدادات العنصر: $e');
      return false;
    }
  }

  /// تبديل حالة توسيع العنصر
  Future<bool> toggleWidgetExpansion(String widgetId) async {
    try {
      final dashboard = currentDashboard.value;
      if (dashboard == null) return false;

      // البحث عن العنصر
      final widgetIndex = dashboard.widgets.indexWhere((w) => w.id == widgetId);
      if (widgetIndex < 0) return false;

      // تحديث حالة توسيع العنصر
      final widget = dashboard.widgets[widgetIndex];
      final updatedWidget = widget.copyWith(
        isExpanded: !widget.isExpanded,
      );

      // إنشاء قائمة جديدة من العناصر
      final updatedWidgets = List<DashboardWidget>.from(dashboard.widgets);
      updatedWidgets[widgetIndex] = updatedWidget;

      // تحديث لوحة المعلومات
      final updatedDashboard = dashboard.copyWith(
        widgets: updatedWidgets,
        updatedAt: DateTime.now(),
      );

      // حفظ لوحة المعلومات
      await _dashboardRepository.saveDashboard(updatedDashboard);

      // تحديث لوحة المعلومات الحالية
      currentDashboard.value = updatedDashboard;

      // تحديث القائمة
      final index = dashboards.indexWhere((d) => d.id == updatedDashboard.id);
      if (index >= 0) {
        dashboards[index] = updatedDashboard;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في تبديل حالة توسيع العنصر: $e');
      return false;
    }
  }

  /// حذف عنصر من لوحة المعلومات
  Future<bool> deleteWidget(String widgetId) async {
    try {
      final dashboard = currentDashboard.value;
      if (dashboard == null) return false;

      // البحث عن العنصر
      final widgetIndex = dashboard.widgets.indexWhere((w) => w.id == widgetId);
      if (widgetIndex < 0) return false;

      // إنشاء قائمة جديدة من العناصر بدون العنصر المحذوف
      final updatedWidgets = List<DashboardWidget>.from(dashboard.widgets);
      updatedWidgets.removeAt(widgetIndex);

      // تحديث لوحة المعلومات
      final updatedDashboard = dashboard.copyWith(
        widgets: updatedWidgets,
        updatedAt: DateTime.now(),
      );

      // حفظ لوحة المعلومات
      await _dashboardRepository.saveDashboard(updatedDashboard);

      // تحديث لوحة المعلومات الحالية
      currentDashboard.value = updatedDashboard;

      // تحديث القائمة
      final index = dashboards.indexWhere((d) => d.id == updatedDashboard.id);
      if (index >= 0) {
        dashboards[index] = updatedDashboard;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في حذف العنصر: $e');
      return false;
    }
  }

  /// إضافة عنصر جديد إلى لوحة المعلومات
  Future<bool> addWidget(DashboardWidget widget) async {
    try {
      final dashboard = currentDashboard.value;
      if (dashboard == null) return false;

      // إنشاء عنصر جديد
      final newWidget = widget.copyWith(
        id: const Uuid().v4(),
      );

      // إضافة العنصر إلى القائمة
      final updatedWidgets = List<DashboardWidget>.from(dashboard.widgets);
      updatedWidgets.add(newWidget);

      // تحديث لوحة المعلومات
      final updatedDashboard = dashboard.copyWith(
        widgets: updatedWidgets,
        updatedAt: DateTime.now(),
      );

      // حفظ لوحة المعلومات
      await _dashboardRepository.saveDashboard(updatedDashboard);

      // تحديث لوحة المعلومات الحالية
      currentDashboard.value = updatedDashboard;

      // تحديث القائمة
      final index = dashboards.indexWhere((d) => d.id == updatedDashboard.id);
      if (index >= 0) {
        dashboards[index] = updatedDashboard;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة العنصر: $e');
      return false;
    }
  }

  /// تحديث عدد صفوف وأعمدة الشبكة
  Future<bool> updateGridDimensions(int rows, int columns) async {
    try {
      final dashboard = currentDashboard.value;
      if (dashboard == null) return false;

      // تحديث لوحة المعلومات
      final updatedDashboard = dashboard.copyWith(
        gridRows: rows,
        gridColumns: columns,
        updatedAt: DateTime.now(),
      );

      // حفظ لوحة المعلومات
      await _dashboardRepository.saveDashboard(updatedDashboard);

      // تحديث لوحة المعلومات الحالية
      currentDashboard.value = updatedDashboard;

      // تحديث القائمة
      final index = dashboards.indexWhere((d) => d.id == updatedDashboard.id);
      if (index >= 0) {
        dashboards[index] = updatedDashboard;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث أبعاد الشبكة: $e');
      return false;
    }
  }

  /// تحديث عنوان ووصف لوحة المعلومات
  Future<bool> updateDashboardInfo(String title, String? description) async {
    try {
      final dashboard = currentDashboard.value;
      if (dashboard == null) return false;

      // تحديث لوحة المعلومات
      final updatedDashboard = dashboard.copyWith(
        title: title,
        description: description,
        updatedAt: DateTime.now(),
      );

      // حفظ لوحة المعلومات
      await _dashboardRepository.saveDashboard(updatedDashboard);

      // تحديث لوحة المعلومات الحالية
      currentDashboard.value = updatedDashboard;

      // تحديث القائمة
      final index = dashboards.indexWhere((d) => d.id == updatedDashboard.id);
      if (index >= 0) {
        dashboards[index] = updatedDashboard;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث معلومات لوحة المعلومات: $e');
      return false;
    }
  }

  /// تعيين لوحة المعلومات الحالية
  void setCurrentDashboard(String dashboardId) {
    final dashboard = dashboards.firstWhereOrNull((d) => d.id == dashboardId);
    if (dashboard != null) {
      currentDashboard.value = dashboard;
    }
  }

  /// تعيين لوحة المعلومات الافتراضية
  Future<bool> setDefaultDashboard(String dashboardId, String userId) async {
    try {
      // تعيين لوحة المعلومات الافتراضية في قاعدة البيانات
      final result = await _dashboardRepository.setDefaultDashboard(dashboardId, userId);

      if (result) {
        // تحديث حالة لوحات المعلومات في الذاكرة
        for (var i = 0; i < dashboards.length; i++) {
          final dashboard = dashboards[i];
          final isDefault = dashboard.id == dashboardId;

          if (dashboard.isDefault != isDefault) {
            dashboards[i] = dashboard.copyWith(isDefault: isDefault);
          }
        }

        // تحديث لوحة المعلومات الحالية إذا كانت هي المتأثرة
        if (currentDashboard.value != null) {
          final isCurrentDefault = currentDashboard.value!.id == dashboardId;
          if (currentDashboard.value!.isDefault != isCurrentDefault) {
            currentDashboard.value = currentDashboard.value!.copyWith(isDefault: isCurrentDefault);
          }
        }
      }

      return result;
    } catch (e) {
      debugPrint('خطأ في تعيين لوحة المعلومات الافتراضية: $e');
      return false;
    }
  }

  /// حذف لوحة المعلومات
  Future<bool> deleteDashboard(String dashboardId) async {
    try {
      // حذف لوحة المعلومات من قاعدة البيانات
      await _dashboardRepository.deleteDashboard(dashboardId);

      // حذف لوحة المعلومات من القائمة
      dashboards.removeWhere((d) => d.id == dashboardId);

      // إذا كانت لوحة المعلومات الحالية هي المحذوفة، نعين لوحة معلومات أخرى
      if (currentDashboard.value?.id == dashboardId) {
        if (dashboards.isNotEmpty) {
          currentDashboard.value = dashboards.first;
        } else {
          currentDashboard.value = null;
        }
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في حذف لوحة المعلومات: $e');
      return false;
    }
  }

  /// مشاركة لوحة المعلومات مع مستخدمين
  Future<bool> shareDashboardWithUsers(List<String> userIds) async {
    try {
      final dashboard = currentDashboard.value;
      if (dashboard == null) return false;

      // تحديث لوحة المعلومات
      final currentSharedUserIds = dashboard.sharedWithUserIds ?? [];
      final newSharedUserIds = [...currentSharedUserIds, ...userIds].toSet().toList();

      final updatedDashboard = dashboard.copyWith(
        isShared: true,
        sharedWithUserIds: newSharedUserIds,
        updatedAt: DateTime.now(),
      );

      // حفظ لوحة المعلومات
      await _dashboardRepository.saveDashboard(updatedDashboard);

      // تحديث لوحة المعلومات الحالية
      currentDashboard.value = updatedDashboard;

      // تحديث القائمة
      final index = dashboards.indexWhere((d) => d.id == updatedDashboard.id);
      if (index >= 0) {
        dashboards[index] = updatedDashboard;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في مشاركة لوحة المعلومات: $e');
      return false;
    }
  }
}
