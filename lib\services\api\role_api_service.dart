import 'package:flutter/foundation.dart';
import '../../models/role_model.dart';
import 'api_service.dart';

/// خدمة API للأدوار - متطابقة مع ASP.NET Core API
class RoleApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع الأدوار
  Future<List<Role>> getAllRoles() async {
    try {
      final response = await _apiService.get('/Roles');
      return _apiService.handleListResponse<Role>(
        response,
        (json) => Role.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأدوار: $e');
      rethrow;
    }
  }

  /// الحصول على دور بواسطة المعرف
  Future<Role?> getRoleById(int id) async {
    try {
      final response = await _apiService.get('/Roles/$id');
      return _apiService.handleResponse<Role>(
        response,
        (json) => Role.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الدور: $e');
      return null;
    }
  }

  /// إنشاء دور جديد
  Future<Role?> createRole(Role role) async {
    try {
      final response = await _apiService.post(
        '/Roles',
        body: role.toJson(),
      );
      return _apiService.handleResponse<Role>(
        response,
        (json) => Role.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء الدور: $e');
      rethrow;
    }
  }

  /// تحديث دور
  Future<Role?> updateRole(Role role) async {
    try {
      final response = await _apiService.put(
        '/Roles/${role.id}',
        body: role.toJson(),
      );
      return _apiService.handleResponse<Role>(
        response,
        (json) => Role.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث الدور: $e');
      rethrow;
    }
  }

  /// حذف دور
  Future<bool> deleteRole(int id) async {
    try {
      final response = await _apiService.delete('/Roles/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف الدور: $e');
      return false;
    }
  }

  /// الحصول على الأدوار النشطة
  Future<List<Role>> getActiveRoles() async {
    try {
      final response = await _apiService.get('/Roles/active');
      return _apiService.handleListResponse<Role>(
        response,
        (json) => Role.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأدوار النشطة: $e');
      rethrow;
    }
  }

  /// البحث في الأدوار
  Future<List<Role>> searchRoles(String query) async {
    try {
      final response = await _apiService.get('/Roles/search?searchTerm=$query');
      return _apiService.handleListResponse<Role>(
        response,
        (json) => Role.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن الأدوار: $e');
      rethrow;
    }
  }

  /// الحصول على صلاحيات دور معين
  Future<List<String>> getRolePermissions(int roleId) async {
    try {
      final response = await _apiService.get('/Roles/$roleId/permissions');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<String>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على صلاحيات الدور: $e');
      return [];
    }
  }

  /// تحديث صلاحيات دور
  Future<bool> updateRolePermissions(int roleId, List<String> permissions) async {
    try {
      final response = await _apiService.put(
        '/Roles/$roleId/permissions',
        body: {'permissions': permissions},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث صلاحيات الدور: $e');
      return false;
    }
  }

  /// الحصول على المستخدمين في دور معين
  Future<List<Map<String, dynamic>>> getRoleUsers(int roleId) async {
    try {
      final response = await _apiService.get('/Roles/$roleId/users');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<Map<String, dynamic>>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على مستخدمي الدور: $e');
      return [];
    }
  }

  /// تعيين دور لمستخدم
  Future<bool> assignRoleToUser(int userId, int roleId) async {
    try {
      final response = await _apiService.post(
        '/Roles/assign',
        body: {
          'userId': userId,
          'roleId': roleId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تعيين الدور للمستخدم: $e');
      return false;
    }
  }

  /// إزالة دور من مستخدم
  Future<bool> removeRoleFromUser(int userId, int roleId) async {
    try {
      final response = await _apiService.delete('/Roles/remove/$userId/$roleId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إزالة الدور من المستخدم: $e');
      return false;
    }
  }

  /// الحصول على جميع الصلاحيات المتاحة
  Future<List<String>> getAvailablePermissions() async {
    try {
      final response = await _apiService.get('/Roles/permissions');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return List<String>.from(response.body as List);
      }
      return [];
    } catch (e) {
      debugPrint('خطأ في الحصول على الصلاحيات المتاحة: $e');
      return [];
    }
  }

  /// تحديث حالة نشاط الدور
  Future<bool> updateRoleActiveStatus(int roleId, bool isActive) async {
    try {
      final response = await _apiService.put(
        '/Roles/$roleId/active',
        body: {'isActive': isActive},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة نشاط الدور: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات الأدوار
  Future<Map<String, dynamic>?> getRoleStatistics() async {
    try {
      final response = await _apiService.get('/Roles/statistics');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return Map<String, dynamic>.from(response.body as Map);
      }
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات الأدوار: $e');
      return null;
    }
  }

  /// نسخ صلاحيات من دور إلى آخر
  Future<bool> copyRolePermissions(int fromRoleId, int toRoleId) async {
    try {
      final response = await _apiService.post(
        '/Roles/copy-permissions',
        body: {
          'fromRoleId': fromRoleId,
          'toRoleId': toRoleId,
        },
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في نسخ صلاحيات الدور: $e');
      return false;
    }
  }

  /// التحقق من وجود صلاحية لدور معين
  Future<bool> hasPermission(int roleId, String permission) async {
    try {
      final response = await _apiService.get('/Roles/$roleId/has-permission?permission=$permission');
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return response.body as bool? ?? false;
      }
      return false;
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية الدور: $e');
      return false;
    }
  }

  /// الحصول على الأدوار الفرعية لدور معين
  Future<List<Role>> getSubRoles(int parentRoleId) async {
    try {
      final response = await _apiService.get('/Roles/$parentRoleId/sub-roles');
      return _apiService.handleListResponse<Role>(
        response,
        (json) => Role.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على الأدوار الفرعية: $e');
      rethrow;
    }
  }

  /// تعيين دور فرعي لدور رئيسي
  Future<bool> assignSubRole(int parentRoleId, int subRoleId) async {
    try {
      final response = await _apiService.post(
        '/Roles/$parentRoleId/sub-roles',
        body: {'subRoleId': subRoleId},
      );
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تعيين الدور الفرعي: $e');
      return false;
    }
  }

  /// إزالة دور فرعي من دور رئيسي
  Future<bool> removeSubRole(int parentRoleId, int subRoleId) async {
    try {
      final response = await _apiService.delete('/Roles/$parentRoleId/sub-roles/$subRoleId');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في إزالة الدور الفرعي: $e');
      return false;
    }
  }
}
