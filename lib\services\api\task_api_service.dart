import 'package:flutter/foundation.dart';
import '../../models/task_model.dart';
import 'api_service.dart';

/// خدمة API للمهام
class TaskApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع المهام
  Future<List<Task>> getAllTasks() async {
    try {
      final response = await _apiService.get('/Tasks');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام: $e');
      rethrow;
    }
  }

  /// الحصول على مهمة بواسطة المعرف
  Future<Task?> getTaskById(int id) async {
    try {
      final response = await _apiService.get('/Tasks/$id');
      return _apiService.handleResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهمة: $e');
      return null;
    }
  }

  /// إنشاء مهمة جديدة
  Future<Task?> createTask(Task task) async {
    try {
      final response = await _apiService.post(
        '/Tasks',
        body: task.toJson(),
      );
      return _apiService.handleResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء المهمة: $e');
      rethrow;
    }
  }

  /// تحديث مهمة
  Future<Task?> updateTask(Task task) async {
    try {
      final response = await _apiService.put(
        '/Tasks/${task.id}',
        body: task.toJson(),
      );
      return _apiService.handleResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث المهمة: $e');
      rethrow;
    }
  }

  /// حذف مهمة (حذف ناعم)
  Future<bool> deleteTask(int id) async {
    try {
      final response = await _apiService.delete('/Tasks/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف المهمة: $e');
      return false;
    }
  }

  /// الحصول على المهام المخصصة لمستخدم معين
  Future<List<Task>> getTasksByAssignee(int assigneeId) async {
    try {
      final response = await _apiService.get('/Tasks/assignee/$assigneeId');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على مهام المستخدم: $e');
      rethrow;
    }
  }

  /// الحصول على المهام بحالة معينة
  Future<List<Task>> getTasksByStatus(int statusId) async {
    try {
      final response = await _apiService.get('/Tasks/status/$statusId');
      return _apiService.handleListResponse<Task>(
        response,
        (json) => Task.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على المهام بالحالة: $e');
      rethrow;
    }
  }

  /// الحصول على جميع حالات المهام
  Future<List<TaskStatus>> getTaskStatuses() async {
    try {
      final response = await _apiService.get('/TaskStatuses');
      return _apiService.handleListResponse<TaskStatus>(
        response,
        (json) => TaskStatus.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على حالات المهام: $e');
      rethrow;
    }
  }

  /// الحصول على جميع أولويات المهام
  Future<List<TaskPriority>> getTaskPriorities() async {
    try {
      final response = await _apiService.get('/TaskPriorities');
      return _apiService.handleListResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أولويات المهام: $e');
      rethrow;
    }
  }

  /// الحصول على جميع أنواع المهام
  Future<List<TaskType>> getTaskTypes() async {
    try {
      final response = await _apiService.get('/TaskTypes');
      return _apiService.handleListResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على أنواع المهام: $e');
      rethrow;
    }
  }

  /// إنشاء حالة مهمة جديدة
  Future<TaskStatus?> createTaskStatus(TaskStatus status) async {
    try {
      final response = await _apiService.post(
        '/TaskStatuses',
        body: status.toJson(),
      );
      return _apiService.handleResponse<TaskStatus>(
        response,
        (json) => TaskStatus.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء حالة المهمة: $e');
      rethrow;
    }
  }

  /// إنشاء أولوية مهمة جديدة
  Future<TaskPriority?> createTaskPriority(TaskPriority priority) async {
    try {
      final response = await _apiService.post(
        '/TaskPriorities',
        body: priority.toJson(),
      );
      return _apiService.handleResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء أولوية المهمة: $e');
      rethrow;
    }
  }

  /// إنشاء نوع مهمة جديد
  Future<TaskType?> createTaskType(TaskType type) async {
    try {
      final response = await _apiService.post(
        '/TaskTypes',
        body: type.toJson(),
      );
      return _apiService.handleResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء نوع المهمة: $e');
      rethrow;
    }
  }

  /// تحديث حالة مهمة
  Future<TaskStatus?> updateTaskStatus(TaskStatus status) async {
    try {
      final response = await _apiService.put(
        '/TaskStatuses/${status.id}',
        body: status.toJson(),
      );
      return _apiService.handleResponse<TaskStatus>(
        response,
        (json) => TaskStatus.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث حالة المهمة: $e');
      rethrow;
    }
  }

  /// تحديث أولوية مهمة
  Future<TaskPriority?> updateTaskPriority(TaskPriority priority) async {
    try {
      final response = await _apiService.put(
        '/TaskPriorities/${priority.id}',
        body: priority.toJson(),
      );
      return _apiService.handleResponse<TaskPriority>(
        response,
        (json) => TaskPriority.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث أولوية المهمة: $e');
      rethrow;
    }
  }

  /// تحديث نوع مهمة
  Future<TaskType?> updateTaskType(TaskType type) async {
    try {
      final response = await _apiService.put(
        '/TaskTypes/${type.id}',
        body: type.toJson(),
      );
      return _apiService.handleResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث نوع المهمة: $e');
      rethrow;
    }
  }

  /// حذف حالة مهمة
  Future<bool> deleteTaskStatus(int id) async {
    try {
      final response = await _apiService.delete('/TaskStatuses/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف حالة المهمة: $e');
      return false;
    }
  }

  /// حذف أولوية مهمة
  Future<bool> deleteTaskPriority(int id) async {
    try {
      final response = await _apiService.delete('/TaskPriorities/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف أولوية المهمة: $e');
      return false;
    }
  }

  /// حذف نوع مهمة
  Future<bool> deleteTaskType(int id) async {
    try {
      final response = await _apiService.delete('/TaskTypes/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف نوع المهمة: $e');
      return false;
    }
  }
}
